#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
ENV ASPNETCORE_HTTP_PORTS=80
ENV ASPNETCORE_URLS=http://*:80
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["src/IT.Microservices.AuthenticationApi/IT.Microservices.AuthenticationApi.csproj", "src/IT.Microservices.AuthenticationApi/"]
RUN dotnet restore "src/IT.Microservices.AuthenticationApi/IT.Microservices.AuthenticationApi.csproj"
COPY . .
WORKDIR "/src/src/IT.Microservices.AuthenticationApi"
RUN dotnet build "IT.Microservices.AuthenticationApi.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "IT.Microservices.AuthenticationApi.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "IT.Microservices.AuthenticationApi.dll"]