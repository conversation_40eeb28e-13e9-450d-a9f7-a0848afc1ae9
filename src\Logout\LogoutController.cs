﻿
using IT.Microservices.AuthenticationApi.ChangePassword;

namespace IT.Microservices.AuthenticationApi.Logout;

public record RevokeTokensRequest(string Email , string AccessToken , string RefreshToken);
public record LogoutRequest(string Email, string AccessToken, string RefreshToken);

public class LogoutController(ILogger<LogoutController> logger, ILogoutUseCase logoutUseCase) : BaseController
{
    [SwaggerOperation(
    Summary = "Revolke tokens api endpoint",
    Description = "Allow an interflora customer to Revoke his access token and RefreshToken through our group octopus api",
    OperationId = "Logout")]
    [SwaggerResponse(200, "", typeof(RevokeTokensRequest))]
    [SwaggerResponse(400, "A parameter is invalid", typeof(Error))]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpPost("revoke")]
    public async Task<IActionResult> RevokeTokensAsync([FromBody] RevokeTokensRequest req)
    {
        if (req is null)
            return BadRequest(new Error("InvalidRequestData", "Request body is null or invalid"));

        logger.LogWarning("Request RevokeToken received : {req}", req.Serialize());
        var res = await logoutUseCase.Process(req);
        return Ok(res.IsSuccess ? res.Value : res.Error);
    }

    [SwaggerOperation(
        Summary = "Logout api endpoint",
        Description = "Allow an interflora customer to Logout through our group octopus api",
        OperationId = "Logout")]
    [SwaggerResponse(200, "", typeof(RevokeTokensRequest))]
    [SwaggerResponse(400, "A parameter is invalid", typeof(Error))]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpPost("")]
    public async Task<IActionResult> LogoutAsync([FromBody] LogoutRequest req)
    {
        if (req is null)
            return BadRequest(new Error("InvalidRequestData", "Request body is null or invalid"));

        logger.LogWarning("Request logout received : {req}", req.Serialize());
        var res = await logoutUseCase.Process(req);
        return Ok(res.IsSuccess ? res.Value : res.Error);
    }
}
