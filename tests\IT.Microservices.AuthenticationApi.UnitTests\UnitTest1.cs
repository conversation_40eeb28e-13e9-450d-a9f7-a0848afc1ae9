using IT.Microservices.AuthenticationApi.Login;
using IT.Microservices.AuthenticationApi.Logout;
using IT.Microservices.AuthenticationApi.CreateUser;
using IT.Microservices.AuthenticationApi.ChangePassword;
using IT.Microservices.AuthenticationApi.DeleteUser;
using IT.Microservices.AuthenticationApi.Common;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using CSharpFunctionalExtensions;
using ITF.Lib.Common.Error;

namespace IT.Microservices.AuthenticationApi.UnitTests;

#region Login Controller Tests

public class LoginControllerTests
{
    private readonly Mock<ILogger<LoginController>> _mockLogger;
    private readonly Mock<ILoginUseCase> _mockLoginUseCase;
    private readonly LoginController _controller;

    public LoginControllerTests()
    {
        _mockLogger = new Mock<ILogger<LoginController>>();
        _mockLoginUseCase = new Mock<ILoginUseCase>();
        _controller = new LoginController(_mockLogger.Object, _mockLoginUseCase.Object);
    }

    [Fact]
    public async Task LoginAsync_ValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new LoginRequest("<EMAIL>", "password123");
        var expectedResult = new JsonObject { ["success"] = true };
        _mockLoginUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Success<JsonObject, AuthenticationFailedResponse>(expectedResult));

        // Act
        var result = await _controller.LoginAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(expectedResult, okResult.Value);
    }

    [Fact]
    public async Task LoginAsync_NullRequest_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.LoginAsync(null!);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
        Assert.IsType<Error>(badRequestResult.Value);
    }

    [Fact]
    public async Task LoginAsync_UseCaseFailure_ReturnsOkWithError()
    {
        // Arrange
        var request = new LoginRequest("<EMAIL>", "wrongpassword");
        var errorResponse = new AuthenticationFailedResponse("invalid_credentials", "Invalid credentials");
        _mockLoginUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Failure<JsonObject, AuthenticationFailedResponse>(errorResponse));

        // Act
        var result = await _controller.LoginAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(errorResponse, okResult.Value);
    }

    [Fact]
    public async Task IntrospectionTokenAsync_ValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new IntrospectionRequest("valid-access-token");
        _mockLoginUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Success<bool, AuthenticationFailedResponse>(true));

        // Act
        var result = await _controller.IntrospectionTokenAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IntrospectionTokenAsync_NullRequest_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.IntrospectionTokenAsync(null!);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
        Assert.IsType<Error>(badRequestResult.Value);
    }

    [Fact]
    public async Task RefreshTokenAsync_ValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new RefreshTokenRequest("valid-refresh-token");
        var expectedResponse = new AuthenticationResponse(
            access_token: "new-access-token",
            expires_in: 300,
            refresh_expires_in: 1800,
            refresh_token: "new-refresh-token",
            token_type: "Bearer",
            id_token: "new-id-token",
            notbeforepolicy: 0,
            session_state: "session-123",
            scope: "openid"
        );
        _mockLoginUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Success<AuthenticationResponse, AuthenticationFailedResponse>(expectedResponse));

        // Act
        var result = await _controller.RefreshTokenAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(expectedResponse, okResult.Value);
    }

    [Fact]
    public async Task RefreshTokenAsync_NullRequest_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.RefreshTokenAsync(null!);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
        Assert.IsType<Error>(badRequestResult.Value);
    }
}

#endregion

#region Request Model Tests

public class LoginRequestTests
{
    [Fact]
    public void LoginRequest_ValidData_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new LoginRequest("<EMAIL>", "password123");

        // Assert
        Assert.Equal("<EMAIL>", request.Email);
        Assert.Equal("password123", request.Password);
    }

    [Fact]
    public void LoginRequest_EmptyEmail_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new LoginRequest("", "password123");

        // Assert
        Assert.Equal("", request.Email);
    }

    [Fact]
    public void LoginRequest_NullPassword_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new LoginRequest("<EMAIL>", null!);

        // Assert
        Assert.Null(request.Password);
    }
}

public class IntrospectionRequestTests
{
    [Fact]
    public void IntrospectionRequest_ValidToken_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new IntrospectionRequest("valid-access-token");

        // Assert
        Assert.Equal("valid-access-token", request.AccessToken);
    }

    [Fact]
    public void IntrospectionRequest_EmptyToken_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new IntrospectionRequest("");

        // Assert
        Assert.Equal("", request.AccessToken);
    }

    [Fact]
    public void IntrospectionRequest_NullToken_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new IntrospectionRequest(null!);

        // Assert
        Assert.Null(request.AccessToken);
    }
}

public class RefreshTokenRequestTests
{
    [Fact]
    public void RefreshTokenRequest_ValidToken_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new RefreshTokenRequest("valid-refresh-token");

        // Assert
        Assert.Equal("valid-refresh-token", request.RefreshToken);
    }

    [Fact]
    public void RefreshTokenRequest_EmptyRefreshToken_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new RefreshTokenRequest("");

        // Assert
        Assert.Equal("", request.RefreshToken);
    }

    [Fact]
    public void RefreshTokenRequest_NullRefreshToken_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new RefreshTokenRequest(null!);

        // Assert
        Assert.Null(request.RefreshToken);
    }
}

#endregion

#region Logout Controller Tests

public class LogoutControllerTests
{
    private readonly Mock<ILogger<LogoutController>> _mockLogger;
    private readonly Mock<ILogoutUseCase> _mockLogoutUseCase;
    private readonly LogoutController _controller;

    public LogoutControllerTests()
    {
        _mockLogger = new Mock<ILogger<LogoutController>>();
        _mockLogoutUseCase = new Mock<ILogoutUseCase>();
        _controller = new LogoutController(_mockLogger.Object, _mockLogoutUseCase.Object);
    }

    [Fact]
    public async Task RevokeTokensAsync_ValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new RevokeTokensRequest("<EMAIL>", "access-token", "refresh-token");
        _mockLogoutUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Success<bool, LogoutFailedResponse>(true));

        // Act
        var result = await _controller.RevokeTokensAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task RevokeTokensAsync_NullRequest_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.RevokeTokensAsync(null!);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
        Assert.IsType<Error>(badRequestResult.Value);
    }

    [Fact]
    public async Task RevokeTokensAsync_UseCaseFailure_ReturnsOkWithError()
    {
        // Arrange
        var request = new RevokeTokensRequest("<EMAIL>", "invalid-token", "invalid-refresh");
        var errorResponse = new LogoutFailedResponse("token_revocation_failed", "Failed to revoke tokens");
        _mockLogoutUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Failure<bool, LogoutFailedResponse>(errorResponse));

        // Act
        var result = await _controller.RevokeTokensAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(errorResponse, okResult.Value);
    }

    [Fact]
    public async Task LogoutAsync_ValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new LogoutRequest("<EMAIL>", "access-token", "refresh-token");
        _mockLogoutUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Success<bool, LogoutFailedResponse>(true));

        // Act
        var result = await _controller.LogoutAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task LogoutAsync_NullRequest_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.LogoutAsync(null!);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
        Assert.IsType<Error>(badRequestResult.Value);
    }
}

public class LogoutRequestTests
{
    [Fact]
    public void RevokeTokensRequest_ValidData_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new RevokeTokensRequest("<EMAIL>", "access-token", "refresh-token");

        // Assert
        Assert.Equal("<EMAIL>", request.Email);
        Assert.Equal("access-token", request.AccessToken);
        Assert.Equal("refresh-token", request.RefreshToken);
    }

    [Fact]
    public void LogoutRequest_ValidData_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new LogoutRequest("<EMAIL>", "access-token", "refresh-token");

        // Assert
        Assert.Equal("<EMAIL>", request.Email);
        Assert.Equal("access-token", request.AccessToken);
        Assert.Equal("refresh-token", request.RefreshToken);
    }

    [Fact]
    public void RevokeTokensRequest_EmptyEmail_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new RevokeTokensRequest("", "access-token", "refresh-token");

        // Assert
        Assert.Equal("", request.Email);
    }

    [Fact]
    public void LogoutRequest_NullTokens_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new LogoutRequest("<EMAIL>", null!, null!);

        // Assert
        Assert.Null(request.AccessToken);
        Assert.Null(request.RefreshToken);
    }
}

#endregion

#region CreateUser Controller Tests

public class CreateUserControllerTests
{
    private readonly Mock<ILogger<CreateUserController>> _mockLogger;
    private readonly Mock<ICreateUserUseCase> _mockCreateUserUseCase;
    private readonly CreateUserController _controller;

    public CreateUserControllerTests()
    {
        _mockLogger = new Mock<ILogger<CreateUserController>>();
        _mockCreateUserUseCase = new Mock<ICreateUserUseCase>();
        _controller = new CreateUserController(_mockLogger.Object, _mockCreateUserUseCase.Object);
    }

    [Fact]
    public async Task CreateUserAsync_ValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new CreateUserRequest("<EMAIL>", "password123");
        var expectedResult = new JsonObject { ["userId"] = "12345", ["success"] = true };
        _mockCreateUserUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Success<JsonObject, CreateUserFailedResponse>(expectedResult));

        // Act
        var result = await _controller.CreateUserAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(expectedResult, okResult.Value);
    }

    [Fact]
    public async Task CreateUserAsync_NullRequest_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.CreateUserAsync(null!);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
        Assert.IsType<Error>(badRequestResult.Value);
    }

    [Fact]
    public async Task CreateUserAsync_UseCaseFailure_ReturnsOkWithError()
    {
        // Arrange
        var request = new CreateUserRequest("<EMAIL>", "password123");
        var errorResponse = new CreateUserFailedResponse("user_already_exists", "User already exists");
        _mockCreateUserUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Failure<JsonObject, CreateUserFailedResponse>(errorResponse));

        // Act
        var result = await _controller.CreateUserAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(errorResponse, okResult.Value);
    }
}

public class CreateUserRequestTests
{
    [Fact]
    public void CreateUserRequest_ValidData_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new CreateUserRequest("<EMAIL>", "password123");

        // Assert
        Assert.Equal("<EMAIL>", request.Email);
        Assert.Equal("password123", request.Password);
    }

    [Fact]
    public void CreateUserRequest_EmptyEmail_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new CreateUserRequest("", "password123");

        // Assert
        Assert.Equal("", request.Email);
    }

    [Fact]
    public void CreateUserRequest_NullPassword_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new CreateUserRequest("<EMAIL>", null!);

        // Assert
        Assert.Null(request.Password);
    }

    [Fact]
    public void CreateUserRequest_WeakPassword_ShouldStillCreate()
    {
        // Arrange & Act
        var request = new CreateUserRequest("<EMAIL>", "123");

        // Assert
        Assert.Equal("<EMAIL>", request.Email);
        Assert.Equal("123", request.Password);
    }
}

#endregion

#region ChangePassword Controller Tests

public class ChangePasswordControllerTests
{
    private readonly Mock<ILogger<ChangePasswordController>> _mockLogger;
    private readonly Mock<IChangePasswordUseCase> _mockChangePasswordUseCase;
    private readonly ChangePasswordController _controller;

    public ChangePasswordControllerTests()
    {
        _mockLogger = new Mock<ILogger<ChangePasswordController>>();
        _mockChangePasswordUseCase = new Mock<IChangePasswordUseCase>();
        _controller = new ChangePasswordController(_mockLogger.Object, _mockChangePasswordUseCase.Object);
    }

    [Fact]
    public async Task ChangePasswordAsync_ValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new ChangePasswordRequest("<EMAIL>", "oldPassword123", "newPassword456");
        _mockChangePasswordUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Success<bool, ChangePasswordFailedResponse>(true));

        // Act
        var result = await _controller.ChangePasswordAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task ChangePasswordAsync_NullRequest_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.ChangePasswordAsync(null!);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
        Assert.IsType<Error>(badRequestResult.Value);
    }

    [Fact]
    public async Task ChangePasswordAsync_UseCaseFailure_ReturnsOkWithError()
    {
        // Arrange
        var request = new ChangePasswordRequest("<EMAIL>", "wrongOldPassword", "newPassword456");
        var errorResponse = new ChangePasswordFailedResponse("invalid_old_password", "Old password is incorrect");
        _mockChangePasswordUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Failure<bool, ChangePasswordFailedResponse>(errorResponse));

        // Act
        var result = await _controller.ChangePasswordAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(errorResponse, okResult.Value);
    }

    [Fact]
    public async Task ChangePasswordAsync_UserNotFound_ReturnsOkWithError()
    {
        // Arrange
        var request = new ChangePasswordRequest("<EMAIL>", "oldPassword123", "newPassword456");
        var errorResponse = new ChangePasswordFailedResponse("UserDoesNotExist", "User <NAME_EMAIL> does not exist in Keycloak");
        _mockChangePasswordUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Failure<bool, ChangePasswordFailedResponse>(errorResponse));

        // Act
        var result = await _controller.ChangePasswordAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(errorResponse, okResult.Value);
    }
}

public class ChangePasswordRequestTests
{
    [Fact]
    public void ChangePasswordRequest_ValidData_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new ChangePasswordRequest("<EMAIL>", "oldPassword123", "newPassword456");

        // Assert
        Assert.Equal("<EMAIL>", request.Email);
        Assert.Equal("oldPassword123", request.OldPassword);
        Assert.Equal("newPassword456", request.NewPassword);
    }

    [Fact]
    public void ChangePasswordRequest_EmptyEmail_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new ChangePasswordRequest("", "oldPassword123", "newPassword456");

        // Assert
        Assert.Equal("", request.Email);
    }

    [Fact]
    public void ChangePasswordRequest_NullPasswords_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new ChangePasswordRequest("<EMAIL>", null!, null!);

        // Assert
        Assert.Null(request.OldPassword);
        Assert.Null(request.NewPassword);
    }

    [Fact]
    public void ChangePasswordRequest_SamePasswords_ShouldStillCreate()
    {
        // Arrange & Act
        var request = new ChangePasswordRequest("<EMAIL>", "password123", "password123");

        // Assert
        Assert.Equal("password123", request.OldPassword);
        Assert.Equal("password123", request.NewPassword);
    }

    [Fact]
    public void ChangePasswordRequest_EmptyNewPassword_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new ChangePasswordRequest("<EMAIL>", "oldPassword123", "");

        // Assert
        Assert.Equal("", request.NewPassword);
    }
}

#endregion

#region DeleteUser Controller Tests

public class DeleteUserControllerTests
{
    private readonly Mock<ILogger<DeleteUserController>> _mockLogger;
    private readonly Mock<IDeleteUserUseCase> _mockDeleteUserUseCase;
    private readonly DeleteUserController _controller;

    public DeleteUserControllerTests()
    {
        _mockLogger = new Mock<ILogger<DeleteUserController>>();
        _mockDeleteUserUseCase = new Mock<IDeleteUserUseCase>();
        _controller = new DeleteUserController(_mockLogger.Object, _mockDeleteUserUseCase.Object);
    }

    [Fact]
    public async Task DeleteUserAsync_ValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new DeleteUserRequest("<EMAIL>", "password123", false);
        _mockDeleteUserUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Success<bool, DeleteUserFailedResponse>(true));

        // Act
        var result = await _controller.DeleteUserAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task DeleteUserAsync_OnlyKeycloakTrue_ReturnsOkResult()
    {
        // Arrange
        var request = new DeleteUserRequest("<EMAIL>", "password123", true);
        _mockDeleteUserUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Success<bool, DeleteUserFailedResponse>(true));

        // Act
        var result = await _controller.DeleteUserAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task DeleteUserAsync_NullRequest_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.DeleteUserAsync(null!);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
        Assert.IsType<Error>(badRequestResult.Value);
    }

    [Fact]
    public async Task DeleteUserAsync_UseCaseFailure_ReturnsOkWithError()
    {
        // Arrange
        var request = new DeleteUserRequest("<EMAIL>", "wrongPassword", false);
        var errorResponse = new DeleteUserFailedResponse("invalid_credentials", "Invalid password provided");
        _mockDeleteUserUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Failure<bool, DeleteUserFailedResponse>(errorResponse));

        // Act
        var result = await _controller.DeleteUserAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(errorResponse, okResult.Value);
    }

    [Fact]
    public async Task DeleteUserAsync_UserNotFound_ReturnsOkWithError()
    {
        // Arrange
        var request = new DeleteUserRequest("<EMAIL>", "password123", false);
        var errorResponse = new DeleteUserFailedResponse("UserDoesNotExist", "User <NAME_EMAIL> does not exist in Keycloak");
        _mockDeleteUserUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Failure<bool, DeleteUserFailedResponse>(errorResponse));

        // Act
        var result = await _controller.DeleteUserAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(errorResponse, okResult.Value);
    }
}

public class DeleteUserRequestTests
{
    [Fact]
    public void DeleteUserRequest_ValidData_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new DeleteUserRequest("<EMAIL>", "password123", false);

        // Assert
        Assert.Equal("<EMAIL>", request.Email);
        Assert.Equal("password123", request.Password);
        Assert.False(request.OnlyKeycloak);
    }

    [Fact]
    public void DeleteUserRequest_OnlyKeycloakTrue_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new DeleteUserRequest("<EMAIL>", "password123", true);

        // Assert
        Assert.Equal("<EMAIL>", request.Email);
        Assert.Equal("password123", request.Password);
        Assert.True(request.OnlyKeycloak);
    }

    [Fact]
    public void DeleteUserRequest_DefaultOnlyKeycloak_IsFalse()
    {
        // Arrange & Act
        var request = new DeleteUserRequest("<EMAIL>", "password123");

        // Assert
        Assert.False(request.OnlyKeycloak);
    }

    [Fact]
    public void DeleteUserRequest_EmptyEmail_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new DeleteUserRequest("", "password123", false);

        // Assert
        Assert.Equal("", request.Email);
    }

    [Fact]
    public void DeleteUserRequest_NullPassword_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new DeleteUserRequest("<EMAIL>", null!, false);

        // Assert
        Assert.Null(request.Password);
    }

    [Fact]
    public void DeleteUserRequest_WhitespaceEmail_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new DeleteUserRequest("   ", "password123", false);

        // Assert
        Assert.Equal("   ", request.Email);
    }
}

#endregion
