# IT.Microservices.AuthenticationApi

A comprehensive authentication microservice built with ASP.NET Core 8.0 that provides secure user authentication, authorization, and user management capabilities through integration with Keycloak and CommerceTools.

## 📋 Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Architecture](#architecture)
- [API Endpoints](#api-endpoints)
- [Configuration](#configuration)
- [Docker Deployment](#docker-deployment)
- [Usage Examples](#usage-examples)
- [Use Cases](#use-cases)
- [Development](#development)
- [Testing](#testing)

## 🔍 Overview

This microservice serves as the central authentication hub for the Interflora ecosystem, providing:

- **User Authentication**: Secure login/logout functionality via Keycloak OAuth2/OpenID Connect
- **Token Management**: Access token refresh, introspection, and revocation
- **User Management**: User creation, password changes, and account deletion
- **Multi-Platform Integration**: Seamless integration with Keycloak and CommerceTools
- **Legacy Support**: Optional integration with Hybris legacy systems (France region)

### Key Technologies

- **Framework**: ASP.NET Core 8.0
- **Authentication**: Keycloak (OAuth2/OpenID Connect)
- **E-commerce Platform**: CommerceTools
- **Database**: CommerceTools Customer Management
- **Monitoring**: Elastic APM, Serilog
- **Feature Flags**: Unleash
- **Testing**: xUnit, Moq (52 comprehensive unit tests)

## ✨ Features

### 🔐 Authentication & Authorization
- OAuth2/OpenID Connect integration with Keycloak
- JWT token-based authentication
- Token refresh mechanism
- Token introspection and validation
- Secure logout with token revocation

### 👤 User Management
- User registration and account creation
- Password change functionality
- Account deletion (Keycloak only or full deletion)
- User profile synchronization between Keycloak and CommerceTools

### 🔄 Token Operations
- Access token refresh using refresh tokens
- Token introspection for validation
- Token revocation for secure logout
- Automatic token expiration handling

### 🌍 Multi-Region Support
- Primary integration with Keycloak and CommerceTools
- Legacy Hybris support for France region
- Configurable store and region-specific settings

## 🏗️ Architecture

### System Architecture Diagram

```mermaid
graph TB
    Client[Client Application] --> API[Authentication API]
    API --> KC[Keycloak<br/>OAuth2/OIDC Provider]
    API --> CT[CommerceTools<br/>Customer Management]
    API --> HY[Hybris Legacy<br/>France Only]

    API --> ELK[Elastic Stack<br/>Logging & APM]
    API --> UN[Unleash<br/>Feature Flags]

    KC --> DB1[(Keycloak DB<br/>User Credentials)]
    CT --> DB2[(CommerceTools<br/>Customer Data)]
    HY --> DB3[(Hybris DB<br/>Legacy Data)]

    subgraph "Authentication Flow"
        direction TB
        A1[1. Login Request] --> A2[2. Validate Credentials]
        A2 --> A3[3. Generate Tokens]
        A3 --> A4[4. Sync User Data]
        A4 --> A5[5. Return Response]
    end
```

### Component Architecture

```mermaid
graph LR
    subgraph "API Layer"
        LC[LoginController]
        LGC[LogoutController]
        CC[CreateUserController]
        CPC[ChangePasswordController]
        DC[DeleteUserController]
    end

    subgraph "Business Logic"
        LU[LoginUseCase]
        LGU[LogoutUseCase]
        CU[CreateUserUseCase]
        CPU[ChangePasswordUseCase]
        DU[DeleteUserUseCase]
    end

    subgraph "External Services"
        KS[KeycloakService]
        CS[CustomerService]
        LS[LegacyService]
    end

    LC --> LU
    LGC --> LGU
    CC --> CU
    CPC --> CPU
    DC --> DU

    LU --> KS
    LU --> CS
    LU --> LS

    LGU --> KS
    CU --> KS
    CU --> CS
    CPU --> KS
    CPU --> CS
    DU --> KS
    DU --> CS
```

## 🚀 API Endpoints

### Base URL
```
https://your-domain.com/itauthenticationapi/api/v1
```

### 🔐 Authentication Endpoints

#### POST `/login`
Authenticate user and return access tokens.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response (Success):**
```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 300,
  "refresh_expires_in": 1800,
  "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "id_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "session_state": "session-uuid",
  "scope": "openid",
  "authentication": {
    "access_token": "...",
    "expires_in": 300
  }
}
```

#### POST `/login/refresh`
Refresh access token using refresh token.

**Request:**
```json
{
  "refreshToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**
```json
{
  "access_token": "new-access-token",
  "expires_in": 300,
  "refresh_expires_in": 1800,
  "refresh_token": "new-refresh-token",
  "token_type": "Bearer",
  "id_token": "new-id-token",
  "session_state": "session-123",
  "scope": "openid"
}
```

#### POST `/login/introspect`
Validate and introspect access token.

**Request:**
```json
{
  "accessToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**
```json
{
  "active": true
}
```

### 🚪 Logout Endpoints

#### POST `/logout`
Complete user logout with token cleanup.

**Request:**
```json
{
  "email": "<EMAIL>",
  "accessToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**
```json
true
```

#### POST `/logout/revoke`
Revoke user tokens without full logout.

**Request:**
```json
{
  "email": "<EMAIL>",
  "accessToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 👤 User Management Endpoints

#### POST `/createuser`
Create a new user account.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:**
```json
{
  "id": "user-uuid",
  "email": "<EMAIL>",
  "firstName": null,
  "lastName": null,
  "isEmailVerified": true,
  "customerGroup": {
    "key": "registered"
  },
  "authentication": {
    "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 300,
    "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer"
  }
}
```

#### POST `/changepassword`
Change user password.

**Request:**
```json
{
  "email": "<EMAIL>",
  "oldPassword": "currentPassword",
  "newPassword": "newSecurePassword123"
}
```

**Response:**
```json
true
```

#### POST `/deleteuser`
Delete user account.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "userPassword",
  "onlyKeycloak": false
}
```

**Response:**
```json
true
```

### 📊 Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "error_code",
  "error_description": "Detailed error message"
}
```

Common error codes:
- `InvalidRequestData`: Request validation failed
- `invalid_credentials`: Authentication failed
- `UserDoesNotExist`: User not found
- `UserAlreadyExist`: User already exists
- `token_expired`: Token has expired
- `token_invalid`: Token is invalid or malformed

## ⚙️ Configuration

### Required Configuration Sections

#### Keycloak Configuration
```json
{
  "Keycloak": {
    "Authority": "https://keycloak.domain.com/realms/REALM_NAME",
    "ClientId": "your-client-id",
    "ClientSecret": "your-client-secret",
    "TokenEndpoint": "https://keycloak.domain.com/realms/REALM_NAME/protocol/openid-connect/token",
    "Url": "https://keycloak.domain.com",
    "Realm": "REALM_NAME",
    "ResponseType": "code",
    "SaveTokens": true,
    "TokenValidationParameters": {
      "NameClaimType": "preferred_username",
      "RoleClaimType": "roles"
    }
  }
}
```

#### Keycloak HTTP Client Configuration
```json
{
  "KeycloakLoginEndpoint": {
    "Url": "https://keycloak.domain.com/realms/REALM_NAME/protocol/openid-connect/token",
    "HttpTimeoutInSeconds": 700,
    "PolicyTimeoutInSeconds": 250,
    "HandlerLifetime": 5,
    "DefaultConnectionLimit": 10
  }
}
```

#### CommerceTools Configuration
```json
{
  "Client": {
    "ClientId": "your-ct-client-id",
    "ClientSecret": "your-ct-client-secret",
    "AuthorizationBaseAddress": "https://auth.region.gcp.commercetools.com/",
    "ProjectKey": "your-project-key",
    "ApiBaseAddress": "https://api.region.gcp.commercetools.com/",
    "StoreProjectionKey": "STORE_KEY",
    "ChannelKey": "channel.key"
  }
}
```

#### Logging Configuration (Serilog + Elastic)
```json
{
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Information"
      }
    },
    "WriteTo": [
      { "Name": "Console" }
    ],
    "Properties": {
      "ApplicationName": "IT.Microservices.AuthenticationApi"
    }
  },
  "ElasticApm": {
    "ServerUrl": "http://apm-server:8200",
    "Enabled": true,
    "TransactionSampleRate": 1,
    "CaptureBody": "all",
    "CaptureHeaders": true
  },
  "ElasticSearchLog": {
    "ElasticSearchLog": "http://elasticsearch:9200/"
  }
}
```

#### Feature Flags Configuration
```json
{
  "Unleash": {
    "Url": "http://unleash:4242/api/",
    "ProjectId": "default",
    "ApplicationName": "IT.Microservices.AuthenticationApi",
    "FetchTogglesIntervalInSeconds": 15,
    "SendMetricsIntervalInSeconds": 30,
    "Environment": "development"
  },
  "FeatureFlags": {
    "Provider": "featuremanager"
  }
}
```

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `ASPNETCORE_ENVIRONMENT` | Application environment | `Production` |
| `ASPNETCORE_URLS` | URLs the app listens on | `http://+:80` |
| `UNLEASH__KEY` | Unleash API key | Required |

## 🐳 Docker Deployment

### Docker Compose Configuration

```yaml
version: '3.8'

services:
  itauthenticationapi:
    image: ${DOCKER_REGISTRY-}itauthenticationapi
    container_name: itauthenticationapi
    build:
      context: .
      dockerfile: src/IT.Microservices.AuthenticationApi/src/Dockerfile
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - UNLEASH__KEY=*:default.your-unleash-key-here
    ports:
      - "99999:80"
    volumes:
      - ${APPDATA}/Microsoft/UserSecrets:/root/.microsoft/usersecrets:ro
      - ${APPDATA}/ASP.NET/Https:/root/.aspnet/https:ro
    depends_on:
      - keycloak
      - elasticsearch
      - unleash
    networks:
      - auth-network

  # Supporting services
  keycloak:
    image: quay.io/keycloak/keycloak:latest
    environment:
      - KEYCLOAK_ADMIN=admin
      - KEYCLOAK_ADMIN_PASSWORD=admin
    ports:
      - "8080:8080"
    networks:
      - auth-network

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    networks:
      - auth-network

  unleash:
    image: unleashorg/unleash-server:latest
    environment:
      - DATABASE_URL=*****************************************/unleash
    ports:
      - "4242:4242"
    networks:
      - auth-network

networks:
  auth-network:
    driver: bridge
```

### Build and Run

```bash
# Build the Docker image
docker build -t itauthenticationapi -f src/Dockerfile .

# Run with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f itauthenticationapi

# Stop services
docker-compose down
```

## 📚 Usage Examples

### Authentication Flow Example

```bash
# 1. Login and get tokens
curl -X POST "http://localhost:99999/itauthenticationapi/api/v1/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securePassword123"
  }'

# Response will include access_token and refresh_token
```

```bash
# 2. Use access token for authenticated requests
curl -X GET "http://your-api/protected-endpoint" \
  -H "Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
```

```bash
# 3. Refresh token when expired
curl -X POST "http://localhost:99999/itauthenticationapi/api/v1/login/refresh" \
  -H "Content-Type: application/json" \
  -d '{
    "refreshToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
  }'
```

```bash
# 4. Logout and revoke tokens
curl -X POST "http://localhost:99999/itauthenticationapi/api/v1/logout" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "accessToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
  }'
```

### User Management Examples

```bash
# Create new user account
curl -X POST "http://localhost:99999/itauthenticationapi/api/v1/createuser" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securePassword123"
  }'
```

```bash
# Change user password
curl -X POST "http://localhost:99999/itauthenticationapi/api/v1/changepassword" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "oldPassword": "currentPassword",
    "newPassword": "newSecurePassword123"
  }'
```

```bash
# Delete user account (Keycloak only)
curl -X POST "http://localhost:99999/itauthenticationapi/api/v1/deleteuser" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "userPassword",
    "onlyKeycloak": true
  }'
```

```bash
# Validate token
curl -X POST "http://localhost:99999/itauthenticationapi/api/v1/login/introspect" \
  -H "Content-Type: application/json" \
  -d '{
    "accessToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
  }'
```

## 🎯 Use Cases

### 1. **E-commerce Customer Authentication**
- **Scenario**: Customer wants to login to the Interflora online store
- **Flow**:
  1. Customer enters email/password
  2. API validates credentials with Keycloak
  3. User profile is synchronized with CommerceTools
  4. JWT tokens are returned for session management
  5. Customer can access protected resources

### 2. **Mobile App Authentication**
- **Scenario**: Mobile app needs to authenticate users
- **Flow**:
  1. App calls `/login` endpoint
  2. Receives access and refresh tokens
  3. Uses access token for API calls
  4. Refreshes token when expired using `/login/refresh`
  5. Logs out using `/logout` endpoint

### 3. **Microservices Token Validation**
- **Scenario**: Other microservices need to validate user tokens
- **Flow**:
  1. Microservice receives request with Bearer token
  2. Calls `/login/introspect` to validate token
  3. Proceeds with request if token is valid
  4. Returns 401 if token is invalid/expired

### 4. **User Registration & Onboarding**
- **Scenario**: New customer wants to create an account
- **Flow**:
  1. Customer provides email and password
  2. API creates user in Keycloak
  3. User profile is created in CommerceTools
  4. Customer group assignments are applied
  5. Welcome email is triggered (external system)

### 5. **Account Management**
- **Scenario**: Customer wants to change password or delete account
- **Flow**:
  1. Customer authenticates with current credentials
  2. For password change: validates old password, updates in Keycloak
  3. For account deletion: removes from Keycloak and optionally CommerceTools
  4. All active sessions are invalidated

### 6. **Legacy System Integration (France)**
- **Scenario**: French customers with existing Hybris accounts
- **Flow**:
  1. Customer attempts login
  2. If not found in Keycloak, checks Hybris
  3. Migrates user data from Hybris to Keycloak/CommerceTools
  4. Returns authentication tokens
  5. Future logins use Keycloak directly

## 🛠️ Development

### Prerequisites
- .NET 8.0 SDK
- Docker & Docker Compose
- Access to Keycloak instance
- CommerceTools project credentials

### Local Development Setup

```bash
# Clone the repository
git clone <repository-url>
cd IT.Microservices.AuthenticationApi

# Restore dependencies
dotnet restore

# Update configuration
cp src/appsettings.Development.json.example src/appsettings.Development.json
# Edit configuration with your settings

# Run the application
dotnet run --project src/IT.Microservices.AuthenticationApi.csproj

# Or use Docker
docker-compose -f docker-compose.dev.yml up
```

### Project Structure
```
src/
├── Common/                 # Shared components
│   ├── BaseController.cs   # Base controller class
│   └── KeycloakLoginHttpService.cs  # Keycloak HTTP client
├── Login/                  # Authentication endpoints
│   ├── LoginController.cs  # Login, refresh, introspect
│   ├── LoginUseCase.cs     # Business logic
│   └── AuthenticationResponse.cs  # Response models
├── Logout/                 # Logout endpoints
├── CreateUser/             # User creation
├── ChangePassword/         # Password management
├── DeleteUser/             # Account deletion
└── Program.cs              # Application startup

tests/
└── IT.Microservices.AuthenticationApi.UnitTests/
    └── UnitTest1.cs        # 52 comprehensive unit tests
```

## 🧪 Testing

### Unit Tests
The project includes comprehensive unit test coverage with **52 tests** covering:

- **Controller Tests**: All 5 controllers with success/failure scenarios
- **Request Validation**: Null checks, empty values, edge cases
- **Business Logic**: Use case integration and error handling
- **Model Tests**: Request/response object validation

```bash
# Run all tests
dotnet test

# Run tests with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run specific test category
dotnet test --filter "Category=ControllerTests"
```

### Test Coverage Breakdown
- **LoginController**: 8 tests (login, refresh, introspect)
- **LogoutController**: 6 tests (logout, revoke tokens)
- **CreateUserController**: 4 tests (user creation scenarios)
- **ChangePasswordController**: 5 tests (password change flows)
- **DeleteUserController**: 6 tests (account deletion options)
- **Request Models**: 23 tests (validation and edge cases)

### Integration Testing
For integration testing, use the provided Docker Compose setup:

```bash
# Start test environment
docker-compose -f docker-compose.test.yml up -d

# Run integration tests
dotnet test --filter "Category=IntegrationTests"

# Cleanup
docker-compose -f docker-compose.test.yml down
```

### API Testing with Postman/Insomnia
Import the provided API collection for manual testing:
- Authentication flows
- Error scenarios
- Token lifecycle management
- User management operations

## 📊 Monitoring & Observability

### Logging
- **Structured Logging**: Serilog with JSON formatting
- **Log Levels**: Configurable per namespace
- **Correlation IDs**: Request tracking across services

### Metrics & APM
- **Elastic APM**: Performance monitoring and distributed tracing
- **Custom Metrics**: Authentication success/failure rates
- **Health Checks**: Endpoint health monitoring

### Feature Flags
- **Unleash Integration**: Runtime feature toggling
- **A/B Testing**: Gradual feature rollouts
- **Circuit Breakers**: Automatic fallback mechanisms

## 🔒 Security Considerations

### Authentication Security
- **OAuth2/OIDC**: Industry-standard authentication protocols
- **JWT Tokens**: Stateless authentication with configurable expiration
- **Refresh Token Rotation**: Enhanced security for long-lived sessions
- **Token Revocation**: Immediate session invalidation

### Data Protection
- **Password Hashing**: Handled by Keycloak (bcrypt)
- **Sensitive Data**: Never logged or exposed in responses
- **HTTPS Only**: All communications encrypted in transit
- **CORS Configuration**: Restricted cross-origin requests

### Compliance
- **GDPR Ready**: User data deletion capabilities
- **Audit Logging**: All authentication events logged
- **Data Minimization**: Only necessary data collected and stored

## 🚀 Deployment & Scaling

### Production Deployment
- **Container Orchestration**: Kubernetes/Docker Swarm ready
- **Load Balancing**: Stateless design supports horizontal scaling
- **Health Checks**: Built-in health endpoints for orchestrators
- **Graceful Shutdown**: Proper cleanup on container termination

### Performance Optimization
- **Connection Pooling**: Efficient HTTP client management
- **Caching**: Token validation caching (when applicable)
- **Async/Await**: Non-blocking I/O operations
- **Resource Limits**: Configurable timeouts and limits

## 📞 Support & Maintenance

### Troubleshooting
- **Logs**: Check application logs for detailed error information
- **Health Endpoints**: `/health` for service status
- **Metrics**: Monitor authentication success rates and response times

### Common Issues
1. **Token Expiration**: Implement proper refresh token handling
2. **Network Timeouts**: Adjust HTTP client timeout settings
3. **User Sync Issues**: Check CommerceTools and Keycloak connectivity
4. **Legacy Integration**: Verify Hybris endpoint configuration (France only)

### Version Compatibility
- **.NET**: 8.0+
- **Keycloak**: 20.0+
- **CommerceTools**: Current API version
- **Docker**: 20.10+

---

## 📄 License

This project is proprietary software owned by Interflora. All rights reserved.

## 🤝 Contributing

For internal development contributions, please follow the established coding standards and ensure all tests pass before submitting pull requests.

---

**🌟 Built with ❤️ by the Interflora Development Team**