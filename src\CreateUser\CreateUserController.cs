﻿namespace IT.Microservices.AuthenticationApi.CreateUser;

public record CreateUserRequest(string Email, string Password);

public class CreateUserController(ILogger<CreateUserController> logger, ICreateUserUseCase createUserUseCase) : BaseController
{
    [SwaggerOperation(
    Summary = "Create User api endpoint",
    Description = "Allow an interflora customer to Create an Account through our group octopus api",
    OperationId = "CreateUser")]
    [SwaggerResponse(200, "", typeof(CreateUserRequest))]
    [SwaggerResponse(400, "A parameter is invalid", typeof(Error))]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpPost("")]
    public async Task<IActionResult> CreateUserAsync([FromBody] CreateUserRequest req)
    {
        if (req is null)
            return BadRequest(new Error("InvalidRequestData", "Request body is null or invalid"));

        logger.LogWarning("Request create user received : {req}", req.Serialize());
        var res = await createUserUseCase.Process(req);
        return Ok(res.IsSuccess ? res.Value : res.Error);
    }
}
