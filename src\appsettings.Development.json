{
  "ElasticApm": {
    "ServerUrl": "http://apm:8200"
  },
  "ElasticSearchLog": {
    "ElasticSearchLog": "http://elasticsearch:9200/"
  },
  "Unleash": {

    "Url": "http://unleash:4242/api/"
  },
  "FeatureFlags": {
    "Provider": "featuremanager"
  },
  "KeycloakLoginEndpoint": {
    "Url": "https://keycloak.recette.interflora.es/realms/OCTOPUS/protocol/openid-connect/token",
    "HttpTimeoutInSeconds": 700,
    "PolicyTimeoutInSeconds": 250,
    "HandlerLifetime": 5,
    "DefaultConnectionLimit": 10
  },
  "Keycloak": {
    "Authority": "https://keycloak.recette.interflora.es/realms/OCTOPUS",
    "ClientId": "authenticationgroup_client",
    "ClientSecret": "tZzqWrPjFoDyDoKxhWmMQALlb7WqI2KG",
    "TokenEndpoint": "https://keycloak.recette.interflora.es/realms/OCTOPUS/protocol/openid-connect/token",
    "LoginPath": "/Account/Login",
    "Url": "https://keycloak.recette.interflora.es",
    "Realm": "OCTOPUS",
    "ResponseType": "code",
    "SaveTokens": true,
    "TokenValidationParameters": {
      "NameClaimType": "preferred_username",
      "RoleClaimType": "roles"
    }
  },
  "Client": {
    "ClientId": "lmhO9haOn3pW1AMxJw0pIceD",
    "ClientSecret": "5Kt5GjOGq8g46A_Yj_GdRDi6VytwUPyk",
    "AuthorizationBaseAddress": "https://auth.europe-west1.gcp.commercetools.com/",
    "ProjectKey": "interfloratest",
    "ApiBaseAddress": "https://api.europe-west1.gcp.commercetools.com/",
    "StoreProjectionKey": "ITE",
    "ChannelKey": "interflora.es"

  }
  //,
  //"HybrisEndpoint": {
  //  "Authentication": {
  //    "Credentials": {
  //      "client_id": "trusted_client",
  //      "client_secret": "secret",
  //      "grant_type": "client_credentials"
  //    },
  //    "URL": "https://preprod-api.interflora.fr/api/oauth/token",
  //    "AuthMethod": "OAUTH",
  //    "UseExpirationTime": true
  //  },
  //  "Url": "https://preprod-api.interflora.fr/",
  //  "HttpTimeoutInSeconds": 700,
  //  "PolicyTimeoutInSeconds": 250,
  //  "HandlerLifetime": 5,
  //  "DefaultConnectionLimit": 10
  //}
}
