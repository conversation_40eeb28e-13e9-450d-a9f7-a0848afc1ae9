# See https://docs.microsoft.com/en-us/azure/devops/pipelines/tasks/test/publish-test-results?view=azure-devops&tabs=yaml#docker
# V.C 15/05/2024

FROM mcr.microsoft.com/dotnet/sdk:8.0
WORKDIR /src

COPY ["src/IT.Microservices.AuthenticationApi.csproj", "/src/IT.Microservices.AuthenticationApi/"]
COPY ["src/NuGet.config", "/src/IT.Microservices.AuthenticationApi/"]

RUN dotnet restore "IT.Microservices.AuthenticationApi/IT.Microservices.AuthenticationApi.csproj"

WORKDIR "/src/IT.Microservices.AuthenticationApi"

COPY . .

RUN dotnet build "src/IT.Microservices.AuthenticationApi.csproj" -c Release
RUN dotnet test "tests/IT.Microservices.AuthenticationApi.UnitTests/IT.Microservices.AuthenticationApi.UnitTests.csproj" -c Release --logger "trx;LogFileName=testresults.trx"; exit 0
RUN dotnet publish "src/IT.Microservices.AuthenticationApi.csproj" -c Release -o out

ENTRYPOINT sleep 10000