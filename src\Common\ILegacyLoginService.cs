﻿using commercetools.Sdk.Api.Models.Customers;
using IT.Microservices.AuthenticationApi.Login;
namespace IT.Microservices.AuthenticationApi.Common;


// This is the interface for the Legacy Login Service that each country that want to real time sync their user db into Keycloak + CT at the login time can implement to check if the user isAuth/exist in the legacy platform
// You can add a Folder with the country name in the Login Folder and add the implementation of this interface for the country (Have a look at the french one)
public interface ILegacyLoginService
{
    // Check if the LoginRequest Provided is OK on the legacy platform (if we can authenticate the user) if yes try to Get the user data and cast it into the Group CT ICustomerDraft object
    Task<Result<ICustomerDraft, AuthenticationFailedResponse>> GetUserFromLegacy(LoginRequest req);
}
