﻿using commercetools.Sdk.Api.Models.Customers;
using FS.Keycloak.RestApiClient.Model;
using IT.Microservices.AuthenticationApi.ChangePassword;
using IT.SharedLibraries.CT.Customers;
using ITF.SharedLibraries.Keycloak;

namespace IT.Microservices.AuthenticationApi.DeleteUser;

public record DeleteUserFailedResponse(string? error = null, string? error_description = null, [property: JsonIgnore] bool? isException = false);


public interface IDeleteUserUseCase
{
    public Task<Result<bool, DeleteUserFailedResponse>> Process(DeleteUserRequest req);
}

public class DeleteUserUseCase(ILogger<DeleteUserUseCase> logger, IConfiguration conf, IKeycloakLoginHttpService keycloakHttpService, ICustomerService custService, SerializerService serializerService, IKeycloakService keycloakService, ILegacyLoginService? legacyLoginService = null) : IDeleteUserUseCase
{
    public async Task<Result<bool, DeleteUserFailedResponse>> Process(DeleteUserRequest req)
    {
        string errorContent = string.Empty;

        if (string.IsNullOrWhiteSpace(req.Email) || string.IsNullOrWhiteSpace(req.Password))
            return new DeleteUserFailedResponse("InvalidRequestData", $"One or more parameters are invalid : req payload received : {req.Serialize()}");

        req = req with { Email = req.Email.Trim().ToLower() };
        // Check if customer exists
        UserRepresentation? user;
        try
        {
            user = await keycloakService.GetKeycloackUserByUsernameOrNullAsync(req.Email);
        }
        catch (Exception e)
        {
            return new DeleteUserFailedResponse { error = e.GetType().ToString(), error_description = string.IsNullOrEmpty(errorContent) ? $"Exception occurred: {e}" : $"{errorContent} with Exception : {e}", isException = true };
        }
        if (user is null)
        {
            errorContent = $"User with email {req.Email} does not exist in Keycloak";
            logger.LogError(errorContent);
            return new DeleteUserFailedResponse { error = "UserDoesNotExist", error_description = errorContent };
        }
        var authUser = await keycloakHttpService.LoginUser(conf["Keycloak:TokenEndpoint"],
            [
                new("client_id", conf["Keycloak:ClientId"]),new("client_secret", conf["Keycloak:ClientSecret"]),
                        new("username", req.Email),new("password", req.Password),
                        new("grant_type", "password"),new("scope", "openid")
            ]);
        if (authUser.IsFailure)
            return new DeleteUserFailedResponse { error = "LoginUserInKeycloakFailed", error_description = "impossible to login the current user before deleting the account" };
        // delete user in Keycloak
        try
        {
            var deleteRes = await keycloakService.DeleteUser(user.Id);
            if (!deleteRes.StatusCode.IsSuccessStatusCode())
            {
                errorContent = $"Error while Deleting the user into Keycloak for email and id : {user.Id} : {req.Email} with the error : {deleteRes.ErrorText}";
                logger.LogError(errorContent);
                return new DeleteUserFailedResponse { error = "DeletingUserInKeycloakFailed", error_description = errorContent };
            }
        }
        catch (Exception e)
        {
            return new DeleteUserFailedResponse { error = "DeletingUserInKeycloakFailed", error_description = string.IsNullOrEmpty(errorContent) ? $"Exception occurred: {e}" : $"{errorContent} with Exception : {e}", isException = true };
        }
        if (!req.OnlyKeycloak)
        {
            // Get customer from commercetools
            var ctUserExists = await custService.GetByEmail(req.Email);
            if (ctUserExists is null)
            {
                errorContent = $"User with email {req.Email} does not exist in commercetools";
                logger.LogError(errorContent);
                return true;
            }

            // delete user in CT
            ICustomer? deletedCust;
            try
            {
                deletedCust = await custService.DeleteCustomer(ctUserExists);
            }
            catch (Exception e)
            {
                logger.LogError(e, "Exception occurred while deleting the user in CT for email {email} with exception : {e}", req.Email, e.ToString());
                return true;
            }
            if(deletedCust == null) { 
                errorContent = $"Error while Deleting the user into CT for email : {req.Email}";
                logger.LogError(errorContent);
                return true;
            }
        }

        return true;
    }
}
