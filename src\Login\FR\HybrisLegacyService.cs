﻿namespace IT.Microservices.AuthenticationApi.Login.FR;

using commercetools.Base.Serialization.JsonConverters;
using commercetools.Sdk.Api.Models.Common;
using commercetools.Sdk.Api.Models.CustomerGroups;
using commercetools.Sdk.Api.Models.Customers;
using commercetools.Sdk.Api.Models.Stores;
using System.Text.Json;
using System.Text.RegularExpressions;
using JsonSerializer = System.Text.Json.JsonSerializer;


#region Models

public record HybrisError(string message,string type);

public record HybrisErrors(IReadOnlyList<HybrisError> errors);

public record HybrisAuthError(string reason,string subject,string type,string message);

public record HybrisAuthErrors(IReadOnlyList<HybrisAuthError> errors);

public record Currency(bool? active,string isocode,string name,string symbol);

public record Country(string isocode,string name);

public record DefaultAddress(bool? defaultAddress,string firstName,string id,string lastName,string line1,string phone,string postalCode,bool? shippingAddress,
    string title,string titleCode,string town, Country? country ,bool? visibleInAddressBook);

public record Language(bool? active,string isocode,string name,string nativeName);

public record HybrisUser(string type,string name,string uid,bool? b2bCustomer,Currency currency,DefaultAddress defaultAddress,string displayUid,string email,
    string firstName,bool? guest, [property: System.Text.Json.Serialization.JsonConverter(typeof(CustomHybrisDateTimeOffsetConverter))] DateTimeOffset? itfPlusExpirationDate,
    Language language,string lastName,bool? premium, [property: System.Text.Json.Serialization.JsonConverter(typeof(CustomHybrisDateTimeOffsetConverter))] DateTimeOffset? premiumSubscriptionDate,string title,string titleCode);

public record HybrisAuthUser(string access_token,string token_type,string refresh_token,int? refresh_token_expires_in,int? expires_in,string scope,string? firstName,string? lastName,string? b2bCustomer,string? phone,string? login,string? title);

#endregion

#region HybrisHttpService
public interface IHybrisHttpService : IHttpClient
{
    public Task<Result<HybrisUser, HybrisErrors>> GetUserByEmail(string email);
    public Task<Result<HybrisAuthUser, HybrisAuthErrors>> LoginCustomer(LoginRequest req);
}

public class HybrisHttpService(System.Net.Http.HttpClient httpClient, IConfiguration config,
    ILogger<HybrisHttpService> logger) : HttpClient(httpClient, config, "HybrisEndpoint", logger), IHybrisHttpService
{
    public async Task<Result<HybrisUser,HybrisErrors>> GetUserByEmail(string email)
    {
        try
        {
            var response = await GetAsync($"api/v2/fr/users/{email}");

            if (response.IsSuccessStatusCode)
                return JsonSerializer.Deserialize<HybrisUser>(await response.Content.ReadAsStringAsync())!;
            else
                return JsonSerializer.Deserialize<HybrisErrors>(await response.Content.ReadAsStringAsync())!;
        }
        catch (Exception e)
        {
            logger.LogError(e, "Exception in the hybris call to GetUser for email : {email} with the exception : {ex}",email,e.ToString());
            return new HybrisErrors([new HybrisError(e.ToString() , "ExceptionApi")]);
        }


    }

    public async Task<Result<HybrisAuthUser, HybrisAuthErrors>> LoginCustomer(LoginRequest req)
    {
        try
        {
            var response = await PostAsync(null,endpoint:$"api/oauth/token?client_id=trusted_client&client_secret=secret&grant_type=password&username={req.Email}&password={req.Password}");

            if (response.IsSuccessStatusCode)
                return JsonSerializer.Deserialize<HybrisAuthUser>(await response.Content.ReadAsStringAsync())!;
            else
                return JsonSerializer.Deserialize<HybrisAuthErrors>(await response.Content.ReadAsStringAsync())!;
        }
        catch (Exception e)
        {
            logger.LogError(e, "Exception in the hybris call to Authorize Customer for email : {email} with the exception : {ex}", req.Email, e.ToString());
            return new HybrisAuthErrors([new HybrisAuthError("ExceptionHybrisApi","username", "InvalidGrantError",e.ToString())]);
        }


    }
}

#endregion

#region implementation of the ILegacyLoginService Common interface

public class ItfLegacyService(IHybrisHttpService hybrisService) : ILegacyLoginService
{
    public async Task<Result<ICustomerDraft, AuthenticationFailedResponse>> GetUserFromLegacy(LoginRequest req)
    {
        try
        {
            var LoginResp = await hybrisService.LoginCustomer(req);

            if (LoginResp.IsSuccess)
            {
                var userDataResp = await hybrisService.GetUserByEmail(req.Email);

                if (userDataResp.IsSuccess)
                    return Result.Success<ICustomerDraft, AuthenticationFailedResponse>(userDataResp.Value.ToGroupCustomer());
                else
                    return userDataResp.Error.ToGroupFailedResponse();

                // TODO here we miss all the others Adress that we can get from the AddressBook api in Hybris see with Majid for the implementation

            }
            else
                return LoginResp.Error.ToGroupFailedResponse();
        }
        catch (Exception)
        {
            return new AuthenticationFailedResponse { error = "GlobalExceptionError", error_description = "Une erreur est survenue merci de réessayer plus tard" };
        }

    }
}

public static class ConverterLegacy
{
    // FR customer mapping to group CT one
    // TODO continue the mapping here
    public static ICustomerDraft ToGroupCustomer(this HybrisUser hybrisUser) => new CustomerDraft
        {
            Email = hybrisUser.email,
            FirstName = hybrisUser.firstName,
            LastName = hybrisUser.lastName,
            Title = hybrisUser.title,
            Addresses = [
                new Address
                {
                    FirstName = hybrisUser.defaultAddress.firstName,
                    LastName = hybrisUser.defaultAddress.lastName,
                    Salutation = hybrisUser.defaultAddress.title,
                    StreetName = hybrisUser.defaultAddress.line1,
                    PostalCode = hybrisUser.defaultAddress.postalCode,
                    City = hybrisUser.defaultAddress.town,
                    Phone = hybrisUser.defaultAddress.phone,
                    Country = hybrisUser.defaultAddress.country?.isocode?.ToUpper() ?? "FR",
                }
            ],
            IsEmailVerified = true,
            AuthenticationMode = IAuthenticationMode.ExternalAuth,
            Stores = [new StoreResourceIdentifier() { Key = "ITF"} ],
            CustomerGroupAssignments = [new CustomerGroupAssignmentDraft() { CustomerGroup = new CustomerGroupResourceIdentifier() { Key = "registered" } }],
    };

    public static AuthenticationFailedResponse ToGroupFailedResponse(this HybrisErrors hybrisErrors) => new()
        {
            error = hybrisErrors?.errors?.FirstOrDefault()?.type ?? "UnknowError",
            error_description = hybrisErrors?.errors?.FirstOrDefault()?.message ?? "Erreur inconnue"
        };

    public static AuthenticationFailedResponse ToGroupFailedResponse(this HybrisAuthErrors hybrisErrors) => new()
    {
        error = hybrisErrors?.errors?.FirstOrDefault()?.type ?? "UnknowError",
        error_description = hybrisErrors?.errors?.FirstOrDefault()?.message ?? "Erreur inconnue"
    };
}

#endregion

#region Json Tools Converter

public class CustomHybrisDateTimeOffsetConverter : System.Text.Json.Serialization.JsonConverter<DateTimeOffset>
{
    public override DateTimeOffset Read(ref Utf8JsonReader reader,Type typeToConvert,JsonSerializerOptions options) =>
        // Add colon to the timezone offset (e.g., "+0100" → "+01:00")
        DateTimeOffset.Parse(Regex.Replace(reader.GetString()!, @"([+-]\d{2})(\d{2})$", "$1:$2"), CultureInfo.InvariantCulture);

    public override void Write(Utf8JsonWriter writer,DateTimeOffset value,JsonSerializerOptions options) =>
        // Remove the colon from the offset for output (optional)
        writer.WriteStringValue(value.ToString("yyyy-MM-dd'T'HH:mm:sszzz").Replace(":", ""));
}
#endregion