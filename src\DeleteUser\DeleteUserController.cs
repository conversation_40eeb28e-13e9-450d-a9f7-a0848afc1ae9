﻿using IT.Microservices.AuthenticationApi.ChangePassword;

namespace IT.Microservices.AuthenticationApi.DeleteUser;

public record DeleteUserRequest(string Email, string Password, bool OnlyKeycloak = false);

public class DeleteUserController(ILogger<DeleteUserController> logger, IDeleteUserUseCase deleteUserUseCase) : BaseController
{
    [SwaggerOperation(
    Summary = "Delete User api endpoint",
    Description = "Allow an interflora customer to Delete their UserAccount in both Keycloak or/and CT through our group octopus api",
    OperationId = "DeleteUser")]
    [SwaggerResponse(200, "", typeof(DeleteUserRequest))]
    [SwaggerResponse(400, "A parameter is invalid", typeof(Error))]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpPost("")]
    public async Task<IActionResult> DeleteUserAsync([FromBody] DeleteUserRequest req)
    {
        if (req is null)
            return BadRequest(new Error("InvalidRequestData", "Request body is null or invalid"));

        logger.LogWarning("Request delete user received : {req}", req.Serialize());
        var res = await deleteUserUseCase.Process(req);
        return Ok(res.IsSuccess ? res.Value : res.Error);
    }
}
