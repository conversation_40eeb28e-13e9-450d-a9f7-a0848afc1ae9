﻿using FS.Keycloak.RestApiClient.Model;
using IT.SharedLibraries.CT.Customers;
using ITF.SharedLibraries.Keycloak;

namespace IT.Microservices.AuthenticationApi.ChangePassword;

public record ChangePasswordFailedResponse(string? error = null, string? error_description = null, [property: JsonIgnore] bool? isException = false);


public interface IChangePasswordUseCase
{
    public Task<Result<bool, ChangePasswordFailedResponse>> Process(ChangePasswordRequest req);
}
public class ChangePasswordUseCase(ILogger<ChangePasswordUseCase> logger, IConfiguration conf, IKeycloakLoginHttpService keycloakHttpService, ICustomerService custService, SerializerService serializerService, IKeycloakService keycloakService, ILegacyLoginService? legacyLoginService = null) : IChangePasswordUseCase
{
    public async Task<Result<bool, ChangePasswordFailedResponse>> Process(ChangePasswordRequest req)
    {
        string errorContent = string.Empty;

        if (string.IsNullOrWhiteSpace(req.Email) || string.IsNullOrWhiteSpace(req.OldPassword) || string.IsNullOrWhiteSpace(req.NewPassword))
            return new ChangePasswordFailedResponse("InvalidRequestData", $"One or more parameters are invalid : req payload received : {req.Serialize()}");

        req = req with { Email = req.Email.Trim().ToLower() };

        // Check if customer exists

        UserRepresentation? user;
        try
        {
            user = await keycloakService.GetKeycloackUserByUsernameOrNullAsync(req.Email);
        }
        catch (Exception e)
        {
            return new ChangePasswordFailedResponse { error = e.GetType().ToString(), error_description = string.IsNullOrEmpty(errorContent) ? $"Exception occurred: {e}" : $"{errorContent} with Exception : {e}", isException = true };
        }

        if (user is null)
        {
            errorContent = $"User with email {req.Email} does not exist in Keycloak";
            logger.LogError(errorContent);
            return new ChangePasswordFailedResponse { error = "UserDoesNotExist", error_description = errorContent };
        }

        // Get customer from commercetools
        var ctUserExists = await custService.GetByEmail(req.Email);
        if (ctUserExists is null)
        {
            errorContent = $"User with email {req.Email} does not exist in commercetools";
            logger.LogError(errorContent);
            return new ChangePasswordFailedResponse { error = "UserDoesNotExist", error_description = errorContent };
        }
        // verify if the old password is correct
        var authUser = await keycloakHttpService.LoginUser(conf["Keycloak:TokenEndpoint"],
            [
                new("client_id", conf["Keycloak:ClientId"]),new("client_secret", conf["Keycloak:ClientSecret"]),
                                            new("username", req.Email),new("password", req.OldPassword),
                                            new("grant_type", "password"),new("scope", "openid")
            ]);

        // return the error from Keycloak
        if (authUser.IsFailure)
        {
            errorContent = $"Error while Login the user into Keycloak for email : {req.Email} with old password with the error : {authUser.Error?.Serialize()}";
            return new ChangePasswordFailedResponse { error = "LoginWithOldPasswordInKeycloakFailed", error_description = errorContent };
        }

        // right now no validation on the password on our side
        // Update the password in keycloak
        try
        {
            var updateRes = await keycloakService.ResetUserPasswordByUserId(user.Id, req.NewPassword);

            if (!updateRes.StatusCode.IsSuccessStatusCode())
            {
                errorContent = $"Error while Reseting the user with the new password into Keycloak for email and id : {user.Id} : {req.Email} with the error : {updateRes.ErrorText}";
                logger.LogError(errorContent);
                return new ChangePasswordFailedResponse { error = "ResettingPasswordInKeycloakFailed", error_description = errorContent };
            }
        }
        catch (Exception e)
        {
            return new ChangePasswordFailedResponse { error = "ResettingPasswordInKeycloakFailed", error_description = string.IsNullOrEmpty(errorContent) ? $"Exception occurred: {e}" : $"{errorContent} with Exception : {e}", isException = true };
        }

        return true;
    }
}
