﻿using commercetools.Sdk.Api.Models.CustomerGroups;
using commercetools.Sdk.Api.Models.Customers;
using commercetools.Sdk.Api.Models.Stores;
using FS.Keycloak.RestApiClient.Model;
using IT.Microservices.AuthenticationApi.Login;
using IT.SharedLibraries.CT.Customers;
using ITF.SharedLibraries.Keycloak;
using System.Text.Json.Nodes;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace IT.Microservices.AuthenticationApi.CreateUser;

public interface ICreateUserUseCase
{
    public Task<Result<JsonObject, CreateUserFailedResponse>> Process(CreateUserRequest req);
}

public class CreateUserUseCase(ILogger<CreateUserUseCase> logger, IConfiguration conf, IKeycloakLoginHttpService keycloakHttpService, ICustomerService custService, SerializerService serializerService, IKeycloakService keycloakService, ILegacyLoginService? legacyLoginService = null) : ICreateUserUseCase
{
    private readonly string? _storeKey = conf?.GetSection("Client:StoreProjectionKey").Value;

    public async Task<Result<JsonObject, CreateUserFailedResponse>> Process(CreateUserRequest req)
    {

        if (string.IsNullOrWhiteSpace(req.Email) || string.IsNullOrWhiteSpace(req.Password))
            return new CreateUserFailedResponse("InvalidRequestData", $"One or more parameters are invalid : req payload received : {req.Serialize()}");

        req = req with { Email = req.Email.Trim().ToLower() };

        string errorContent = string.Empty;
        UserRepresentation? user;
        try
        {
            user = await keycloakService.GetKeycloackUserByUsernameOrNullAsync(req.Email);
        }
        catch (Exception e)
        {
            return new CreateUserFailedResponse { error = e.GetType().ToString(), error_description = string.IsNullOrEmpty(errorContent) ? $"Exception occurred: {e}" : $"{errorContent} with Exception : {e}", isException = true };
        }

        if (user is not null)
        {
            errorContent = $"User with email {req.Email} already exists in Keycloak";
            logger.LogError(errorContent);
            return new CreateUserFailedResponse { error = "UserAlreadyExist" , error_description = errorContent };
        }

        // right now no validation on the password on our side

        var keycloakRes = await keycloakService.CreateUser(new UserRepresentation
        {
            Enabled = true,
            EmailVerified = true,
            Username = req.Email,
            Email = req.Email,
            Credentials = [ new CredentialRepresentation { Type = "password", Temporary = false, Value = req.Password } ]
        });

        if (!keycloakRes.StatusCode.IsSuccessStatusCode())
        {
            errorContent = $"Error while creating the user into Keycloak for email : {req.Email} with the error : {keycloakRes.ErrorText}";
            logger.LogError(errorContent);
            return new CreateUserFailedResponse { error = "CreationInKeycloakFailed", error_description = errorContent };
        }

        // at this step we have created the customer in Keycloak so we can login the User to get an access token
        var authUser = await keycloakHttpService.LoginUser(conf["Keycloak:TokenEndpoint"],
            [
                new("client_id", conf["Keycloak:ClientId"]),new("client_secret", conf["Keycloak:ClientSecret"]),
                                new("username", req.Email),new("password", req.Password),
                                new("grant_type", "password"),new("scope", "openid")
            ]);

        // return the error from Keycloak
        if (authUser.IsFailure)
        {
            errorContent = $"Error while Login the user into Keycloak for email : {req.Email} after having created his account with the error : {keycloakRes.ErrorText}";
            return new CreateUserFailedResponse { error = "LoginAfterCreationInKeycloakFailed", error_description = errorContent };
        }

        // first try to verify if the user exist in CT
        var ctUserExists = await custService.GetByEmail(req.Email);

        if (ctUserExists is null)
        {
            var ctUserDraft = new CustomerDraft
            {
                Email = req.Email,
                //FirstName = req.FirstName,
                //LastName = req.LastName,
                //Title = req.Title,
                Addresses = [],
                IsEmailVerified = true,
                AuthenticationMode = IAuthenticationMode.ExternalAuth,
                Stores = [new StoreResourceIdentifier() { Key = _storeKey }],
                CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey },
                CustomerGroupAssignments = [new CustomerGroupAssignmentDraft() { CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey } }],

            };

            var ctRes = await custService.CreateCustomer(ctUserDraft);

            if (ctRes is null)
            {
                errorContent = $"Error while creating the user with payload draft : {ctUserDraft.Serialize(Serializer.SerializerType.CommerceTools,serializerService)} in CT from request into CT for email : {req.Email}";
                logger.LogError(errorContent);
                return new CreateUserFailedResponse { error = "CreationInCTFailed", error_description = errorContent };
            }

            // at this step we have the customer in Keycloak and also logged in and created in CT so we can return the user data + authentication token
            return new JsonObject(
                JsonNode.Parse(serializerService.Serialize(ctRes))!.AsObject().ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.DeepClone()))
                {
                    ["authentication"] = JsonSerializer.SerializeToNode(authUser.GetValueOrDefault())
                };
        }
        else // case where the user already exist in CT
        {
            logger.LogWarning("User with data from CT : {payload} with email {email} already exists in CT, update process to change customerGroups", ctUserExists.Serialize(Serializer.SerializerType.CommerceTools,serializerService), req.Email);

            var customerUpdateActions = new List<ICustomerUpdateAction>();

            // if no stores are assigned to the user, we add the store of the current country (we authorize only 1 country by account right now)
            if (ctUserExists.Stores is null || ctUserExists.Stores.Count == 0)
            {
                logger.LogWarning("User with email {email} already exists in CT but has no stores, adding store {storeKey}", req.Email, _storeKey);
                customerUpdateActions.Add(new CustomerAddStoreAction() { Store = new StoreResourceIdentifier() { Key = _storeKey } });
            }
            else // the use already has stores assigned, we can check if the store is already assigned
            {
                if(ctUserExists.Stores.All(s => s.Key != _storeKey))
                {
                    logger.LogWarning("User with email {email} already exists in CT but does not have store {storeKey} current stores : {stores} , adding it", req.Email, _storeKey , ctUserExists.Stores?.Serialize(Serializer.SerializerType.CommerceTools,serializerService));
                    customerUpdateActions.Add(new CustomerAddStoreAction() { Store = new StoreResourceIdentifier() { Key = _storeKey } });
                }
            }

            // if the user is not in the registered customer group, we add it
            if (ctUserExists.CustomerGroupAssignments is null || ctUserExists.CustomerGroupAssignments.All(cga => cga.CustomerGroup?.Obj?.Key != CustomerCommonValues.RegisteredCustomerGroupKey))
            {
                logger.LogWarning("User with email {email} already exists in CT but does not have customer group 'registered', adding it", req.Email);
                customerUpdateActions.Add(new CustomerAddCustomerGroupAssignmentAction { CustomerGroupAssignment = new CustomerGroupAssignmentDraft() { CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey } } });
            }

            // if the user is in the anonymous customer group, we remove it
            if (ctUserExists.CustomerGroupAssignments is not null && ctUserExists.CustomerGroupAssignments.Any(cga => cga.CustomerGroup?.Obj?.Key == CustomerCommonValues.AnonymousCustomerGroupKey))
            {
                logger.LogWarning("User with email {email} already exists in CT but has customer group 'anonymous', removing it", req.Email);
                customerUpdateActions.Add(new CustomerRemoveCustomerGroupAssignmentAction { CustomerGroup =  new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.AnonymousCustomerGroupKey } });
            }

            if(ctUserExists.CustomerGroup == null)
            {
                logger.LogWarning("User with email {email} already exists in CT but has no customer group, set it with 'registered' group", req.Email);
                customerUpdateActions.Add(new CustomerSetCustomerGroupAction { CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey } });
            }
            else
            {
                // also verify the classical customer group property and replace it if needed (if it's anonymous)
                if (ctUserExists.CustomerGroup?.Obj?.Key == CustomerCommonValues.AnonymousCustomerGroupKey)
                {
                    logger.LogWarning("User with email {email} already exists in CT but has customer group '{customerGroup}', replacing it with 'registered'", req.Email, ctUserExists.CustomerGroup?.Obj?.Key);
                    customerUpdateActions.Add(new CustomerSetCustomerGroupAction { CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey } });
                }
            }

            if (customerUpdateActions.Count == 0)
            { 
                logger.LogInformation("User with email {email} already exists in CT and no update actions are needed", req.Email);
                // no update actions needed, we can return the user data + authentication token
                return new JsonObject(
                    JsonNode.Parse(serializerService.Serialize(ctUserExists))!.AsObject().ToDictionary(
                        kvp => kvp.Key,
                        kvp => kvp.Value?.DeepClone()))
                {
                    ["authentication"] = JsonSerializer.SerializeToNode(authUser.GetValueOrDefault())
                };
            }

            var updatedCust = await custService.UpdateCustomer(ctUserExists,customerUpdateActions);

            // update failed
            if (updatedCust == null)
            {
                errorContent = $"Error while updating the user : {ctUserExists.Serialize(Serializer.SerializerType.CommerceTools , serializerService)} with payload actions update : {customerUpdateActions.Serialize(Serializer.SerializerType.CommerceTools, serializerService)} in CT from request into CT for email : {req.Email}";
                logger.LogError(errorContent);
                return new CreateUserFailedResponse { error = "UpdateInCTFailed", error_description = errorContent };
            }

            // at this step we have the customer in Keycloak and also logged in and updated in CT so we can return the user data + authentication token
            return new JsonObject(
                JsonNode.Parse(serializerService.Serialize(updatedCust))!.AsObject().ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.DeepClone()))
            {
                ["authentication"] = JsonSerializer.SerializeToNode(authUser.GetValueOrDefault())
            };
        }

    }
}
