﻿using IT.Microservices.AuthenticationApi.ChangePassword;
using IT.Microservices.AuthenticationApi.CreateUser;

namespace IT.Microservices.AuthenticationApi.ChangePassword;

public record ChangePasswordRequest(string Email, string OldPassword, string NewPassword);

public class ChangePasswordController(ILogger<ChangePasswordController> logger, IChangePasswordUseCase changePasswordUseCase) : BaseController
{
    [SwaggerOperation(
    Summary = "Change Password api endpoint",
    Description = "Allow an interflora customer to Change their Password through our group octopus api",
    OperationId = "ChangePassword")]
    [SwaggerResponse(200, "", typeof(ChangePasswordRequest))]
    [SwaggerResponse(400, "A parameter is invalid", typeof(Error))]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpPost("")]
    public async Task<IActionResult> ChangePasswordAsync([FromBody] ChangePasswordRequest req)
    {
        if (req is null)
            return BadRequest(new Error("InvalidRequestData", "Request body is null or invalid"));

        logger.LogWarning("Request change password received : {req}", req.Serialize());
        var res = await changePasswordUseCase.Process(req);
        return Ok(res.IsSuccess ? res.Value : res.Error);
    }
}
