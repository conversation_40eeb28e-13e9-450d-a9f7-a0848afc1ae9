Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IT.Microservices.AuthenticationApi", "src\IT.Microservices.AuthenticationApi.csproj", "{66E52C54-1008-3C66-A2AB-5C868F591651}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IT.Microservices.AuthenticationApi.UnitTests", "tests\IT.Microservices.AuthenticationApi.UnitTests\IT.Microservices.AuthenticationApi.UnitTests.csproj", "{85AACD51-406C-A706-0D7E-BCA285811E28}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{66E52C54-1008-3C66-A2AB-5C868F591651}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{66E52C54-1008-3C66-A2AB-5C868F591651}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{66E52C54-1008-3C66-A2AB-5C868F591651}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{66E52C54-1008-3C66-A2AB-5C868F591651}.Release|Any CPU.Build.0 = Release|Any CPU
		{85AACD51-406C-A706-0D7E-BCA285811E28}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{85AACD51-406C-A706-0D7E-BCA285811E28}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{85AACD51-406C-A706-0D7E-BCA285811E28}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{85AACD51-406C-A706-0D7E-BCA285811E28}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{85AACD51-406C-A706-0D7E-BCA285811E28} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {E9E791DD-DAF0-4D91-91CC-45F70447AB89}
	EndGlobalSection
EndGlobal
