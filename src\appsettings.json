{
  "AllowedHosts": "*",
  "Serilog": {
    "Using": [],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Information",
        "Elastic": "Warning",
        "Apm": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console"
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithProcessId",
      "WithThreadId"
    ],
    "Properties": {
      "ApplicationName": "IT.Microservices.AuthenticationApi"
    }
  },
  "ElasticApm": {
    "ServerUrl": "**********",
	"Enabled": true,
    "TransactionSampleRate": 1,
    "CaptureBody": "all",
    "CaptureHeaders": true,
    "SpanFramesMinDuration": 0, // no stacktrace except for exception
    "CloudProvider": "none"
  },
  "ElasticSearchLog": {
    "ElasticSearchLog": "**********"
  },
  "Unleash": {
    "Url": "**********",
    "ProjectId": "default",
    "ApplicationName": "IT.Microservices.AuthenticationApi",
    "FetchTogglesIntervalInSeconds": 15,
    "SendMetricsIntervalInSeconds": 30,
    "Environment": "development"
  },
  "FeatureFlags": {
    "Provider": "featuremanager"
  }
}
