﻿using IT.Microservices.AuthenticationApi.Login;
using System.Net;
using System.Net.Http.Headers;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace IT.Microservices.AuthenticationApi.Common;

public interface IKeycloakLoginHttpService : IHttpClient
{
    public Task<Result<AuthenticationResponse, AuthenticationFailedResponse>> LoginUser(string? Url , List<KeyValuePair<string, string?>> formData);
    Task<bool> CompleteLogoutAsync(string accessToken, string refreshToken,string? idToken = null);
    Task<bool> PerformCompleteLogoutWithVerification(string userId, string accessToken, string refreshToken);
    Task<bool> RevokeUserTokensAsync(string accessToken, string? refreshToken = null);
    Task<Result<IntrospectionResponse, AuthenticationFailedResponse>> IntrospectTokenAsync(string token);
}

public class KeycloakLoginHttpService(System.Net.Http.HttpClient httpClient, IConfiguration config,
    ILogger<KeycloakLoginHttpService> logger) : HttpClient(httpClient, config, "KeycloakLoginEndpoint", logger), IKeycloakLoginHttpService
{
    public async Task<Result<AuthenticationResponse, AuthenticationFailedResponse>> LoginUser(string? Url, List<KeyValuePair<string, string?>> formData)
    {
        try
        {
            var content = new FormUrlEncodedContent(formData);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/x-www-form-urlencoded");

            var response = await _client.PostAsync(Url ?? _uri?.ToString(), content);

            if (response.IsSuccessStatusCode)
                return JsonSerializer.Deserialize<AuthenticationResponse>(await response.Content.ReadAsStringAsync())!;
            else
                return JsonSerializer.Deserialize<AuthenticationFailedResponse>(await response.Content.ReadAsStringAsync())!;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "The Process for getting the token api of keycloak to login the user through the KeycloakHttpService throw an exception : {ex}",ex.ToString());
            return new AuthenticationFailedResponse {error = ex.GetType().ToString(),error_description = ex.ToString(), isException = true };
        }
    }


    #region Logout and Token Management Methods


    /// <summary>
    /// Performs complete logout by revoking tokens AND terminating sessions
    /// </summary>
    public async Task<bool> CompleteLogoutAsync(string accessToken,string refreshToken,string? idToken = null)
    {
        var allSuccess = true;

        // Step 1: Revoke the access token
        var accessRevoked = await RevokeTokenAsync(accessToken, "access_token");
        allSuccess &= accessRevoked;

        // Step 2: Revoke the refresh token (this also invalidates associated tokens)
        var refreshRevoked = await RevokeTokenAsync(refreshToken, "refresh_token");
        allSuccess &= refreshRevoked;

        // Step 3: Call logout endpoint to terminate sessions
        bool logoutSuccess;
        if (!string.IsNullOrEmpty(idToken))
        {
            // Preferred: Use id_token_hint for OIDC-compliant logout
            logoutSuccess = await LogoutWithIdTokenAsync(idToken);
        }
        else
        {
            // Fallback: Use refresh token for legacy logout
            logoutSuccess = await LogoutWithRefreshTokenAsync(refreshToken);
        }
        allSuccess &= logoutSuccess;

        return allSuccess;
    }

    public async Task<bool> PerformCompleteLogoutWithVerification(string userId, string accessToken, string refreshToken)
    {
        // 1. Revoke tokens first (prevents new sessions)
        await RevokeTokenAsync(accessToken, "access_token");
        await RevokeTokenAsync(refreshToken, "refresh_token");

        // 3. Optional: Force logout via Admin API for complete assurance
        await ForceUserLogoutEverywhereAsync(userId);

        // 4. Verify token is no longer valid
        var isStillValid = await IntrospectTokenAsync(accessToken);

        if(isStillValid.IsSuccess)
        {
            // Token is still valid if its "active" field is true so we want to return false if its true
            return !isStillValid.Value.active;
        }
        else
        {
            // If introspection failed, we assume the token is maybe still valid (could be another error)
            return false;
        }
    }

    public async Task<bool> RevokeUserTokensAsync(string accessToken , string? refreshToken = null)
    {
        var accessRevoked = await RevokeTokenAsync(accessToken, "access_token");
        var refreshRevoked = await RevokeTokenAsync(refreshToken, "refresh_token");
        return accessRevoked && refreshRevoked;
    }

    private async Task<bool> RevokeTokenAsync(string token, string tokenTypeHint)
    {
        var revokeUrl = $"{config["Keycloak:Url"]}/realms/{config["Keycloak:Realm"]}/protocol/openid-connect/revoke";

        var parameters = new List<KeyValuePair<string, string>>
        {
            new("token", token),
            new("client_id", config["Keycloak:ClientId"] ?? ""),
            new("client_secret", config["Keycloak:ClientSecret"] ?? ""),
            new("token_type_hint", tokenTypeHint)
        };

        var content = new FormUrlEncodedContent(parameters);

        try
        {
            var response = await _client.PostAsync(revokeUrl, content);
            // 200 OK means success OR token was already invalid
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            // Log error
            logger.LogError(ex, "Error revoking token: {ex}", ex.ToString());
            return false;
        }
    }

    public async Task<Result<IntrospectionResponse, AuthenticationFailedResponse>> IntrospectTokenAsync(string token)
    {
        try
        {
            var introspectUrl = $"{config["Keycloak:Url"]}/realms/{config["Keycloak:Realm"]}/protocol/openid-connect/token/introspect";

            var parameters = new FormUrlEncodedContent(
            [
                new KeyValuePair<string, string>("token", token),
                new KeyValuePair<string, string>("client_id", config["Keycloak:ClientId"] ?? ""),
                new KeyValuePair<string, string>("client_secret", config["Keycloak:ClientSecret"] ?? "")
            ]);
            parameters.Headers.ContentType = new MediaTypeHeaderValue("application/x-www-form-urlencoded");

            var response = await _client.PostAsync(introspectUrl, parameters);

            if (response.IsSuccessStatusCode)
                return JsonSerializer.Deserialize<IntrospectionResponse>(await response.Content.ReadAsStringAsync())!;
            else
                return JsonSerializer.Deserialize<AuthenticationFailedResponse>(await response.Content.ReadAsStringAsync())!;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error introspecting token: {ex}", ex.ToString());
            return new AuthenticationFailedResponse { error = ex.GetType().ToString(), error_description = ex.ToString(), isException = true };
        }

    }

    private async Task<bool> LogoutWithIdTokenAsync(string idToken)
    {
        var logoutUrl = $"{config["Keycloak:Url"]}/realms/{config["Keycloak:Realm"]}/protocol/openid-connect/logout";

        // Build URL with query parameters for GET request
        var queryParams = $"?id_token_hint={idToken}&client_id={config["Keycloak:ClientId"]}";
        var fullUrl = logoutUrl + queryParams;

        try
        {
            // Use custom handler to prevent automatic redirect following
            using var handler = new HttpClientHandler { AllowAutoRedirect = false };
            using var tempClient = new System.Net.Http.HttpClient(handler);

            var response = await tempClient.GetAsync(fullUrl);

            // 200, 302, or 303 all indicate successful logout initiation
            return response.IsSuccessStatusCode ||
                   response.StatusCode == HttpStatusCode.Found ||
                   response.StatusCode == HttpStatusCode.SeeOther;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error logging out with ID token: {ex}", ex.ToString());
            return false;
        }
    }

    private async Task<bool> LogoutWithRefreshTokenAsync(string refreshToken)
    {
        var logoutUrl = $"{config["Keycloak:Url"]}/realms/{config["Keycloak:Realm"]}/protocol/openid-connect/logout";

        var parameters = new List<KeyValuePair<string, string>>
        {
            new("refresh_token", refreshToken),
            new("client_id", config["Keycloak:ClientId"] ?? ""),
            new("client_secret", config["Keycloak:ClientSecret"] ?? "")
        };

        var content = new FormUrlEncodedContent(parameters);

        try
        {
            var response = await _client.PostAsync(logoutUrl, content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error logging out with refresh token: {ex}", ex.ToString());
            return false;
        }
    }

    private async Task<bool> ForceUserLogoutEverywhereAsync(string userId)
    {
        // Step 1: Get admin access token
        var adminToken = await GetAdminTokenAsync();

        if (adminToken.IsFailure)
        {
            logger.LogError("Failed to obtain admin token: {error} - {description}",
                adminToken.Error.error, adminToken.Error.error_description);
            return false;
        }

        // Step 2: Force logout the user (terminates ALL sessions and invalidates tokens)
        var logoutUrl = $"{config["Keycloak:Url"]}/admin/realms/{config["Keycloak:Realm"]}/users/{userId}/logout";

        _client.DefaultRequestHeaders.Authorization =
            new AuthenticationHeaderValue("Bearer", adminToken.Value.access_token);

        try
        {
            var response = await _client.PostAsync(logoutUrl, null);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error forcing user logout: {ex}", ex.ToString());
            return false;
        }
    }

    private async Task<Result<AuthenticationResponse, AuthenticationFailedResponse>> GetAdminTokenAsync()
    {
        try
        {
            var tokenUrl = $"{config["Keycloak:Url"]}/realms/{config["Keycloak:Realm"]}/protocol/openid-connect/token";

            var parameters = new FormUrlEncodedContent(
            [
                new KeyValuePair<string, string>("grant_type", "client_credentials"),
                new KeyValuePair<string, string>("client_id", config["Keycloak:ClientId"] ?? ""),
                new KeyValuePair<string, string>("client_secret", config["Keycloak:ClientSecret"] ?? "")
            ]);
            parameters.Headers.ContentType = new MediaTypeHeaderValue("application/x-www-form-urlencoded");

            var response = await _client.PostAsync(tokenUrl, parameters);

            if (response.IsSuccessStatusCode)
                return JsonSerializer.Deserialize<AuthenticationResponse>(await response.Content.ReadAsStringAsync())!;
            else
                return JsonSerializer.Deserialize<AuthenticationFailedResponse>(await response.Content.ReadAsStringAsync())!;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "The Process for getting the Admin token api of keycloak to login As an Admin api through the KeycloakHttpService throw an exception : {ex}", ex.ToString());
            return new AuthenticationFailedResponse { error = ex.GetType().ToString(), error_description = ex.ToString(), isException = true };
        }
    }

    #endregion
}

public static class KeycloakHelper
{
    public static bool IsSuccessStatusCode(this HttpStatusCode statusCode) => ((int)statusCode >= 200) && ((int)statusCode <= 299);
}
