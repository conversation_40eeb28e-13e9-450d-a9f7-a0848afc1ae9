<?xml version="1.0"?>
<doc>
    <assembly>
        <name>IT.Microservices.AuthenticationApi</name>
    </assembly>
    <members>
        <member name="M:IT.Microservices.AuthenticationApi.Common.KeycloakLoginHttpService.CompleteLogoutAsync(System.String,System.String,System.String)">
            <summary>
            Performs complete logout by revoking tokens AND terminating sessions
            </summary>
        </member>
    </members>
</doc>
