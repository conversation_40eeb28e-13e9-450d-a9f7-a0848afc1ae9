
namespace IT.Microservices.AuthenticationApi.Login;

public record LoginRequest(string Email, string Password);

public record IntrospectionRequest(string AccessToken);

public record RefreshTokenRequest(string RefreshToken);

public class LoginController(ILogger<LoginController> logger , ILoginUseCase loginUseCase) : BaseController
{
    [SwaggerOperation(
        Summary = "Login api endpoint",
        Description = "Allow an interflora customer to Login through our group octopus api",
        OperationId = "Login")]
    [SwaggerResponse(200, "", typeof(LoginRequest))]
    [SwaggerResponse(400, "A parameter is invalid", typeof(Error))]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpPost("")]
    public async Task<IActionResult> LoginAsync([FromBody] LoginRequest req)
    {
        if(req is null)
            return BadRequest(new Error("InvalidRequestData", "Request body is null or invalid"));

        logger.LogWarning("Request login received : {req}", req.Serialize());
        var res = await loginUseCase.Process(req);
        return Ok(res.IsSuccess ? res.Value : res.Error);
    }

    [SwaggerOperation(
    Summary = "Introspection of a token api endpoint",
    Description = "Allow a client to verify that an token from keycloak is still valid through our group octopus api",
    OperationId = "Login")]
    [SwaggerResponse(200, "", typeof(LoginRequest))]
    [SwaggerResponse(400, "A parameter is invalid", typeof(Error))]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpPost("introspect")]
    public async Task<IActionResult> IntrospectionTokenAsync([FromBody] IntrospectionRequest req)
    {
        if (req is null)
            return BadRequest(new Error("InvalidRequestData", "Request body is null or invalid"));

        logger.LogWarning("Request Introspection token received : {req}", req.Serialize());
        var res = await loginUseCase.Process(req);
        return Ok(res.IsSuccess ? res.Value : res.Error);
    }

    [SwaggerOperation(
        Summary = "Refresh token api endpoint",
        Description = "Allow a client to refresh an access token by providing a valid refresh token through our group octopus api",
        OperationId = "RefreshToken")]
    [SwaggerResponse(200, "", typeof(RefreshTokenRequest))]
    [SwaggerResponse(400, "A parameter is invalid", typeof(Error))]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpPost("refresh")]
    public async Task<IActionResult> RefreshTokenAsync([FromBody] RefreshTokenRequest req)
    {
        if (req is null)
            return BadRequest(new Error("InvalidRequestData", "Request body is null or invalid"));

        logger.LogWarning("Request refresh token received : {req}", req.Serialize());
        var res = await loginUseCase.Process(req);
        return Ok(res.IsSuccess ? res.Value : res.Error);
    }
}
