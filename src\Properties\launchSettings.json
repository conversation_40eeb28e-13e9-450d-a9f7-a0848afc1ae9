{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:50606", "sslPort": 44355}}, "$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": false, "launchUrl": "", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "IT.Microservices.AuthenticationApi": {"commandName": "Project", "launchBrowser": false, "launchUrl": "", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "https://localhost:5001;http://localhost:5000"}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": false, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "publishAllPorts": true, "useSSL": false}}}