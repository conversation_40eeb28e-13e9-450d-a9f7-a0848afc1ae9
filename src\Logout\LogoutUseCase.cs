﻿using FS.Keycloak.RestApiClient.Model;
using IT.SharedLibraries.CT.Customers;
using ITF.SharedLibraries.Keycloak;

namespace IT.Microservices.AuthenticationApi.Logout;

public record LogoutFailedResponse(string? error = null, string? error_description = null, [property: JsonIgnore] bool? isException = false);


public interface ILogoutUseCase
{
    public Task<Result<bool, LogoutFailedResponse>> Process(RevokeTokensRequest req);
    public Task<Result<bool, LogoutFailedResponse>> Process(LogoutRequest req);
}

public class LogoutUseCase(ILogger<LogoutUseCase> logger, IConfiguration conf, IKeycloakLoginHttpService keycloakHttpService, ICustomerService custService, SerializerService serializerService, IKeycloakService keycloakService, ILegacyLoginService? legacyLoginService = null) : ILogoutUseCase
{
    public async Task<Result<bool, LogoutFailedResponse>> Process(RevokeTokensRequest req)    
    {
        string errorContent = string.Empty;

        if (string.IsNullOrWhiteSpace(req.Email) || string.IsNullOrWhiteSpace(req.AccessToken))
            return new LogoutFailedResponse("InvalidRequestData", $"One or more parameters are invalid : req payload received : {req.Serialize()}");

        req = req with { Email = req.Email.Trim().ToLower() };

        // Check if customer exists
        UserRepresentation? user;
        try
        {
            user = await keycloakService.GetKeycloackUserByUsernameOrNullAsync(req.Email);
        }
        catch (Exception e)
        {
            return new LogoutFailedResponse { error = e.GetType().ToString(), error_description = string.IsNullOrEmpty(errorContent) ? $"Exception occurred: {e}" : $"{errorContent} with Exception : {e}", isException = true };
        }
        if (user is null)
        {
            errorContent = $"User with email {req.Email} does not exist in Keycloak";
            logger.LogError(errorContent);
            return new LogoutFailedResponse { error = "UserDoesNotExist", error_description = errorContent };
        }
        try
        {
            var logoutRes = await keycloakHttpService.RevokeUserTokensAsync(req.AccessToken,req.RefreshToken);
            if (logoutRes)
            {
                logger.LogInformation("User with email {email} successfully logged out (Token revoked) from Keycloak", req.Email);
                return true;
            }
            else
            {
                errorContent = $"Failed to revoked tokens user with email {req.Email} from Keycloak";
                logger.LogError(errorContent);
                return new LogoutFailedResponse { error = "RevokeTokensFailed", error_description = errorContent };
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "The Process for Revoke Tokens for the user with email={email} through the KeycloakService throw an exception : {ex}", req.Email, ex.ToString());
            return new LogoutFailedResponse { error = ex.GetType().ToString(), error_description = ex.ToString(), isException = true };
        }
    }

    public async Task<Result<bool, LogoutFailedResponse>> Process(LogoutRequest req)
    {
        string errorContent = string.Empty;

        if (string.IsNullOrWhiteSpace(req.Email) || string.IsNullOrWhiteSpace(req.AccessToken))
            return new LogoutFailedResponse("InvalidRequestData", $"One or more parameters are invalid : req payload received : {req.Serialize()}");

        req = req with { Email = req.Email.Trim().ToLower() };

        // Check if customer exists
        UserRepresentation? user;
        try
        {
            user = await keycloakService.GetKeycloackUserByUsernameOrNullAsync(req.Email);
        }
        catch (Exception e)
        {
            return new LogoutFailedResponse { error = e.GetType().ToString(), error_description = string.IsNullOrEmpty(errorContent) ? $"Exception occurred: {e}" : $"{errorContent} with Exception : {e}", isException = true };
        }
        if (user is null)
        {
            errorContent = $"User with email {req.Email} does not exist in Keycloak";
            logger.LogError(errorContent);
            return new LogoutFailedResponse { error = "UserDoesNotExist", error_description = errorContent };
        }
        try
        {
            var logoutRes = await keycloakHttpService.PerformCompleteLogoutWithVerification(user.Id,req.AccessToken, req.RefreshToken);
            if (logoutRes)
            {
                logger.LogInformation("User with email {email} successfully logged out from Keycloak", req.Email);
                return true;
            }
            else
            {
                errorContent = $"Failed to Logout user with email {req.Email} from Keycloak";
                logger.LogError(errorContent);
                return new LogoutFailedResponse { error = "LogoutFailed", error_description = errorContent };
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "The Process for logging out the user with email={email} through the KeycloakService throw an exception : {ex}", req.Email, ex.ToString());
            return new LogoutFailedResponse { error = ex.GetType().ToString(), error_description = ex.ToString(), isException = true };
        }
    }
}
